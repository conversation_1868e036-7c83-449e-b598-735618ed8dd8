//
//  SearchViewController_Swift.swift
//  Augment
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2025/8/7.
//

import UIKit

class SearchViewController_Swift: UIViewController {
    
    // MARK: - Properties
    private var searchBar: UISearchBar!
    private var tableView: UITableView!
    private var dataManager: SearchDataManager!
    private var currentDisplayData: [SearchItem] = []
    private var isSearching = false
    
    // MARK: - Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        setupData()
    }
    
    // MARK: - UI Setup
    private func setupUI() {
        view.backgroundColor = .systemBackground
        title = "搜索 (Swift)"
        
        // 设置导航栏
        navigationItem.leftBarButtonItem = UIBarButtonItem(
            title: "返回",
            style: .plain,
            target: self,
            action: #selector(backButtonTapped)
        )
        
        // 创建搜索栏
        searchBar = UISearchBar()
        searchBar.placeholder = "搜索"
        searchBar.delegate = self
        searchBar.showsCancelButton = true
        view.addSubview(searchBar)
        
        // 创建表格视图
        tableView = UITableView()
        tableView.dataSource = self
        tableView.delegate = self
        tableView.backgroundColor = .systemBackground
        tableView.register(UITableViewCell.self, forCellReuseIdentifier: "SearchCell")
        view.addSubview(tableView)
        
        setupConstraints()
    }
    
    private func setupConstraints() {
        searchBar.translatesAutoresizingMaskIntoConstraints = false
        tableView.translatesAutoresizingMaskIntoConstraints = false
        
        NSLayoutConstraint.activate([
            searchBar.topAnchor.constraint(equalTo: view.safeAreaLayoutGuide.topAnchor),
            searchBar.leadingAnchor.constraint(equalTo: view.leadingAnchor),
            searchBar.trailingAnchor.constraint(equalTo: view.trailingAnchor),
            
            tableView.topAnchor.constraint(equalTo: searchBar.bottomAnchor),
            tableView.leadingAnchor.constraint(equalTo: view.leadingAnchor),
            tableView.trailingAnchor.constraint(equalTo: view.trailingAnchor),
            tableView.bottomAnchor.constraint(equalTo: view.bottomAnchor)
        ])
    }
    
    // MARK: - Data Setup
    private func setupData() {
        dataManager = SearchDataManager.shared()
        loadInitialData()
    }
    
    private func loadInitialData() {
        isSearching = false
        var allData: [SearchItem] = []
        
        // 添加搜索历史
        allData.append(contentsOf: dataManager.searchHistory)
        
        // 添加猜你想搜
        allData.append(contentsOf: dataManager.suggestions)
        
        // 添加搜索热点
        allData.append(contentsOf: dataManager.hotTopics)
        
        currentDisplayData = allData
        tableView.reloadData()
    }
    
    // MARK: - Actions
    @objc private func backButtonTapped() {
        navigationController?.popViewController(animated: true)
    }
    
    // MARK: - Helper Methods
    private func getSectionData() -> [[SearchItem]] {
        var sections: [[SearchItem]] = []
        
        if !dataManager.searchHistory.isEmpty {
            sections.append(dataManager.searchHistory)
        }
        
        if !dataManager.suggestions.isEmpty {
            sections.append(dataManager.suggestions)
        }
        
        if !dataManager.hotTopics.isEmpty {
            sections.append(dataManager.hotTopics)
        }
        
        return sections
    }
    
    private func getSectionTitle(for section: Int) -> String? {
        let sections = getSectionData()
        guard section < sections.count else { return nil }
        
        var currentSection = 0
        
        if !dataManager.searchHistory.isEmpty {
            if section == currentSection { return "搜索历史" }
            currentSection += 1
        }
        
        if !dataManager.suggestions.isEmpty {
            if section == currentSection { return "猜你想搜" }
            currentSection += 1
        }
        
        if !dataManager.hotTopics.isEmpty {
            if section == currentSection { return "搜索热点" }
        }
        
        return nil
    }
    
    private func getItem(at indexPath: IndexPath) -> SearchItem? {
        if isSearching {
            guard indexPath.row < currentDisplayData.count else { return nil }
            return currentDisplayData[indexPath.row]
        } else {
            let sections = getSectionData()
            guard indexPath.section < sections.count,
                  indexPath.row < sections[indexPath.section].count else { return nil }
            return sections[indexPath.section][indexPath.row]
        }
    }
}

// MARK: - UISearchBarDelegate
extension SearchViewController_Swift: UISearchBarDelegate {
    
    func searchBar(_ searchBar: UISearchBar, textDidChange searchText: String) {
        if searchText.isEmpty {
            loadInitialData()
        } else {
            isSearching = true
            currentDisplayData = dataManager.searchItems(withKeyword: searchText)
            tableView.reloadData()
        }
    }
    
    func searchBarCancelButtonClicked(_ searchBar: UISearchBar) {
        searchBar.text = ""
        searchBar.resignFirstResponder()
        loadInitialData()
    }
    
    func searchBarSearchButtonClicked(_ searchBar: UISearchBar) {
        searchBar.resignFirstResponder()
        
        if let searchText = searchBar.text, !searchText.isEmpty {
            // 添加到搜索历史
            let historyItem = SearchItem(
                id: "search_\(Date().timeIntervalSince1970)",
                title: searchText,
                subtitle: "",
                type: .history,
                status: .normal,
                timestamp: Date(),
                popularity: 50
            )
            dataManager.addSearchHistoryItem(historyItem)
        }
    }
}

// MARK: - UITableViewDataSource
extension SearchViewController_Swift: UITableViewDataSource {
    
    func numberOfSections(in tableView: UITableView) -> Int {
        if isSearching {
            return 1
        }
        return getSectionData().count
    }
    
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        if isSearching {
            return currentDisplayData.count
        }
        let sections = getSectionData()
        guard section < sections.count else { return 0 }
        return sections[section].count
    }
    
    func tableView(_ tableView: UITableView, titleForHeaderInSection section: Int) -> String? {
        if isSearching {
            return "搜索结果"
        }
        return getSectionTitle(for: section)
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let cell = tableView.dequeueReusableCell(withIdentifier: "SearchCell", for: indexPath)
        
        guard let item = getItem(at: indexPath) else { return cell }
        
        cell.textLabel?.text = item.title
        cell.detailTextLabel?.text = item.subtitle.isEmpty ? nil : item.subtitle
        
        // 根据类型设置不同的图标
        switch item.type {
        case .history:
            cell.imageView?.image = UIImage(systemName: "clock")
        case .suggestion:
            cell.imageView?.image = UIImage(systemName: "magnifyingglass")
        case .hotTopic:
            cell.imageView?.image = UIImage(systemName: "flame")
        @unknown default:
            cell.imageView?.image = UIImage(systemName: "questionmark")
        }
        
        // 设置副标题颜色
        if !item.subtitle.isEmpty {
            switch item.subtitle {
            case "热":
                cell.detailTextLabel?.textColor = .systemRed
            case "荐":
                cell.detailTextLabel?.textColor = .systemOrange
            case "新":
                cell.detailTextLabel?.textColor = .systemBlue
            default:
                cell.detailTextLabel?.textColor = .systemGray
            }
        }
        
        return cell
    }
}

// MARK: - UITableViewDelegate
extension SearchViewController_Swift: UITableViewDelegate {
    
    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        tableView.deselectRow(at: indexPath, animated: true)
        
        guard let item = getItem(at: indexPath) else { return }
        
        // 显示详情或执行搜索
        let alert = UIAlertController(
            title: "选中项目",
            message: "您选择了：\(item.title)",
            preferredStyle: .alert
        )
        alert.addAction(UIAlertAction(title: "确定", style: .default))
        present(alert, animated: true)
    }
    
    // 支持滑动删除（仅搜索历史）
    func tableView(_ tableView: UITableView, canEditRowAt indexPath: IndexPath) -> Bool {
        if isSearching { return false }
        
        // 只有搜索历史支持删除
        let sections = getSectionData()
        if indexPath.section < sections.count {
            let sectionItems = sections[indexPath.section]
            if indexPath.row < sectionItems.count {
                return sectionItems[indexPath.row].type == .history
            }
        }
        return false
    }
    
    func tableView(_ tableView: UITableView, commit editingStyle: UITableViewCell.EditingStyle, forRowAt indexPath: IndexPath) {
        if editingStyle == .delete {
            guard let item = getItem(at: indexPath) else { return }
            dataManager.removeSearchHistoryItem(item)
            loadInitialData()
        }
    }
}
