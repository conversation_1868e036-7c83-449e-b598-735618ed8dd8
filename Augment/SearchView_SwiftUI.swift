//
//  SearchView_SwiftUI.swift
//  Augment
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2025/8/7.
//

import SwiftUI

// MARK: - SearchView_SwiftUI
struct SearchView_SwiftUI: View {
    @StateObject private var viewModel = SearchViewModel()
    @State private var searchText = ""
    @State private var showingAlert = false
    @State private var selectedItem: SearchItem?
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // 搜索栏
                SearchBar(text: $searchText, onSearchButtonClicked: {
                    viewModel.addToSearchHistory(searchText)
                })
                .padding(.horizontal)
                
                // 搜索结果列表
                List {
                    if searchText.isEmpty {
                        // 显示分类数据
                        if !viewModel.searchHistory.isEmpty {
                            Section("搜索历史") {
                                ForEach(viewModel.searchHistory, id: \.itemId) { item in
                                    SearchItemRow(item: item) {
                                        selectedItem = item
                                        showingAlert = true
                                    }
                                    .swipeActions(edge: .trailing, allowsFullSwipe: true) {
                                        But<PERSON>("删除", role: .destructive) {
                                            viewModel.removeFromSearchHistory(item)
                                        }
                                    }
                                }
                            }
                        }
                        
                        if !viewModel.suggestions.isEmpty {
                            Section("猜你想搜") {
                                ForEach(viewModel.suggestions, id: \.itemId) { item in
                                    SearchItemRow(item: item) {
                                        selectedItem = item
                                        showingAlert = true
                                    }
                                }
                            }
                        }
                        
                        if !viewModel.hotTopics.isEmpty {
                            Section("搜索热点") {
                                ForEach(viewModel.hotTopics, id: \.itemId) { item in
                                    SearchItemRow(item: item) {
                                        selectedItem = item
                                        showingAlert = true
                                    }
                                }
                            }
                        }
                    } else {
                        // 显示搜索结果
                        Section("搜索结果") {
                            ForEach(viewModel.searchResults, id: \.itemId) { item in
                                SearchItemRow(item: item) {
                                    selectedItem = item
                                    showingAlert = true
                                }
                            }
                        }
                    }
                }
                .listStyle(PlainListStyle())
            }
            .navigationTitle("搜索 (SwiftUI)")
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarBackButtonHidden(true)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("返回") {
                        // 这里需要在集成时处理返回逻辑
                    }
                }
            }
        }
        .onChange(of: searchText) { newValue in
            viewModel.search(keyword: newValue)
        }
        .alert("选中项目", isPresented: $showingAlert) {
            Button("确定", role: .cancel) { }
        } message: {
            if let item = selectedItem {
                Text("您选择了：\(item.title)")
            }
        }
    }
}

// MARK: - SearchBar
struct SearchBar: View {
    @Binding var text: String
    let onSearchButtonClicked: () -> Void
    
    var body: some View {
        HStack {
            HStack {
                Image(systemName: "magnifyingglass")
                    .foregroundColor(.gray)
                
                TextField("搜索", text: $text)
                    .onSubmit {
                        if !text.isEmpty {
                            onSearchButtonClicked()
                        }
                    }
                
                if !text.isEmpty {
                    Button(action: {
                        text = ""
                    }) {
                        Image(systemName: "xmark.circle.fill")
                            .foregroundColor(.gray)
                    }
                }
            }
            .padding(.horizontal, 10)
            .padding(.vertical, 8)
            .background(Color(.systemGray6))
            .cornerRadius(10)
            
            if !text.isEmpty {
                Button("取消") {
                    text = ""
                    UIApplication.shared.sendAction(#selector(UIResponder.resignFirstResponder), to: nil, from: nil, for: nil)
                }
                .foregroundColor(.blue)
            }
        }
        .padding(.vertical, 8)
    }
}

// MARK: - SearchItemRow
struct SearchItemRow: View {
    let item: SearchItem
    let onTap: () -> Void
    
    var body: some View {
        HStack {
            // 图标
            Image(systemName: iconName)
                .foregroundColor(iconColor)
                .frame(width: 20, height: 20)
            
            VStack(alignment: .leading, spacing: 2) {
                Text(item.title)
                    .font(.body)
                    .foregroundColor(.primary)
                
                if !item.subtitle.isEmpty {
                    Text(item.subtitle)
                        .font(.caption)
                        .foregroundColor(subtitleColor)
                }
            }
            
            Spacer()
        }
        .contentShape(Rectangle())
        .onTapGesture {
            onTap()
        }
    }
    
    private var iconName: String {
        switch item.type {
        case .history:
            return "clock"
        case .suggestion:
            return "magnifyingglass"
        case .hotTopic:
            return "flame"
        @unknown default:
            return "questionmark"
        }
    }
    
    private var iconColor: Color {
        switch item.type {
        case .history:
            return .gray
        case .suggestion:
            return .blue
        case .hotTopic:
            return .orange
        @unknown default:
            return .gray
        }
    }
    
    private var subtitleColor: Color {
        switch item.subtitle {
        case "热":
            return .red
        case "荐":
            return .orange
        case "新":
            return .blue
        default:
            return .gray
        }
    }
}

// MARK: - SearchViewModel
class SearchViewModel: ObservableObject {
    @Published var searchHistory: [SearchItem] = []
    @Published var suggestions: [SearchItem] = []
    @Published var hotTopics: [SearchItem] = []
    @Published var searchResults: [SearchItem] = []
    
    private let dataManager = SearchDataManager.shared()
    
    init() {
        loadData()
    }
    
    private func loadData() {
        searchHistory = dataManager.searchHistory
        suggestions = dataManager.suggestions
        hotTopics = dataManager.hotTopics
    }
    
    func search(keyword: String) {
        if keyword.isEmpty {
            searchResults = []
        } else {
            searchResults = dataManager.searchItems(withKeyword: keyword)
        }
    }
    
    func addToSearchHistory(_ text: String) {
        guard !text.isEmpty else { return }
        
        let historyItem = SearchItem(
            id: "search_\(Date().timeIntervalSince1970)",
            title: text,
            subtitle: "",
            type: .history,
            status: .normal,
            timestamp: Date(),
            popularity: 50
        )
        
        dataManager.addSearchHistoryItem(historyItem)
        loadData() // 重新加载数据
    }
    
    func removeFromSearchHistory(_ item: SearchItem) {
        dataManager.removeSearchHistoryItem(item)
        loadData() // 重新加载数据
    }
}

// MARK: - UIViewController for SwiftUI Integration
@objc class SearchViewController_SwiftUI: UIViewController {

    override func viewDidLoad() {
        super.viewDidLoad()

        let swiftUIView = SearchView_SwiftUI()
        let hostingController = UIHostingController(rootView: swiftUIView)

        // 添加为子视图控制器
        addChild(hostingController)
        view.addSubview(hostingController.view)
        hostingController.didMove(toParent: self)

        // 设置约束
        hostingController.view.translatesAutoresizingMaskIntoConstraints = false
        NSLayoutConstraint.activate([
            hostingController.view.topAnchor.constraint(equalTo: view.topAnchor),
            hostingController.view.leadingAnchor.constraint(equalTo: view.leadingAnchor),
            hostingController.view.trailingAnchor.constraint(equalTo: view.trailingAnchor),
            hostingController.view.bottomAnchor.constraint(equalTo: view.bottomAnchor)
        ])

        title = "搜索 (SwiftUI)"
    }
}

// MARK: - Preview
struct SearchView_SwiftUI_Previews: PreviewProvider {
    static var previews: some View {
        SearchView_SwiftUI()
    }
}
