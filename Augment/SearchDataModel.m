//
//  SearchDataModel.m
//  Augment
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2025/8/7.
//

#import "SearchDataModel.h"

@implementation SearchItem

- (instancetype)initWithId:(NSString *)itemId
                     title:(NSString *)title
                  subtitle:(NSString *)subtitle
                      type:(SearchItemType)type
                    status:(SearchItemStatus)status
                 timestamp:(NSDate *)timestamp
                popularity:(NSInteger)popularity {
    self = [super init];
    if (self) {
        _itemId = itemId;
        _title = title;
        _subtitle = subtitle;
        _type = type;
        _status = status;
        _timestamp = timestamp;
        _popularity = popularity;
    }
    return self;
}

@end

@interface SearchDataManager ()

@property (nonatomic, strong) NSMutableArray<SearchItem *> *mutableSearchHistory;
@property (nonatomic, strong) NSMutableArray<SearchItem *> *mutableSuggestions;
@property (nonatomic, strong) NSMutableArray<SearchItem *> *mutableHotTopics;

@end

@implementation SearchDataManager

+ (instancetype)sharedManager {
    static SearchDataManager *sharedInstance = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        sharedInstance = [[SearchDataManager alloc] init];
        [sharedInstance loadTestData];
    });
    return sharedInstance;
}

- (instancetype)init {
    self = [super init];
    if (self) {
        _mutableSearchHistory = [[NSMutableArray alloc] init];
        _mutableSuggestions = [[NSMutableArray alloc] init];
        _mutableHotTopics = [[NSMutableArray alloc] init];
    }
    return self;
}

#pragma mark - Getters

- (NSArray<SearchItem *> *)searchHistory {
    return [self.mutableSearchHistory copy];
}

- (NSArray<SearchItem *> *)suggestions {
    return [self.mutableSuggestions copy];
}

- (NSArray<SearchItem *> *)hotTopics {
    return [self.mutableHotTopics copy];
}

#pragma mark - Search Methods

- (NSArray<SearchItem *> *)searchItemsWithKeyword:(NSString *)keyword {
    if (!keyword || keyword.length == 0) {
        return @[];
    }
    
    NSMutableArray *results = [[NSMutableArray alloc] init];
    NSString *lowercaseKeyword = [keyword lowercaseString];
    
    // 搜索历史
    for (SearchItem *item in self.mutableSearchHistory) {
        if ([[item.title lowercaseString] containsString:lowercaseKeyword] ||
            [[item.subtitle lowercaseString] containsString:lowercaseKeyword]) {
            [results addObject:item];
        }
    }
    
    // 搜索建议
    for (SearchItem *item in self.mutableSuggestions) {
        if ([[item.title lowercaseString] containsString:lowercaseKeyword] ||
            [[item.subtitle lowercaseString] containsString:lowercaseKeyword]) {
            [results addObject:item];
        }
    }
    
    // 热点搜索
    for (SearchItem *item in self.mutableHotTopics) {
        if ([[item.title lowercaseString] containsString:lowercaseKeyword] ||
            [[item.subtitle lowercaseString] containsString:lowercaseKeyword]) {
            [results addObject:item];
        }
    }
    
    // 按热度排序
    [results sortUsingComparator:^NSComparisonResult(SearchItem *obj1, SearchItem *obj2) {
        return [@(obj2.popularity) compare:@(obj1.popularity)];
    }];
    
    return [results copy];
}

- (void)addSearchHistoryItem:(SearchItem *)item {
    // 避免重复添加
    for (SearchItem *existingItem in self.mutableSearchHistory) {
        if ([existingItem.itemId isEqualToString:item.itemId]) {
            [self.mutableSearchHistory removeObject:existingItem];
            break;
        }
    }
    
    // 添加到开头
    [self.mutableSearchHistory insertObject:item atIndex:0];
    
    // 限制历史记录数量
    if (self.mutableSearchHistory.count > 20) {
        [self.mutableSearchHistory removeLastObject];
    }
}

- (void)removeSearchHistoryItem:(SearchItem *)item {
    [self.mutableSearchHistory removeObject:item];
}

- (void)clearSearchHistory {
    [self.mutableSearchHistory removeAllObjects];
}

#pragma mark - Data Operation Methods

- (void)markItemAsRead:(SearchItem *)item {
    item.status = SearchItemStatusRead;
}

- (void)markItemAsUnread:(SearchItem *)item {
    item.status = SearchItemStatusUnread;
}

- (void)deleteItem:(SearchItem *)item {
    [self.mutableSearchHistory removeObject:item];
    [self.mutableSuggestions removeObject:item];
    [self.mutableHotTopics removeObject:item];
}

#pragma mark - Test Data

- (void)loadTestData {
    NSDate *now = [NSDate date];
    NSTimeInterval dayInterval = 24 * 60 * 60;
    
    // 搜索历史数据
    [self.mutableSearchHistory addObjectsFromArray:@[
        [[SearchItem alloc] initWithId:@"history_1" title:@"直达" subtitle:@"" type:SearchItemTypeHistory status:SearchItemStatusNormal timestamp:[now dateByAddingTimeInterval:-dayInterval] popularity:85],
        [[SearchItem alloc] initWithId:@"history_2" title:@"怀孕前三个月不能说出..." subtitle:@"" type:SearchItemTypeHistory status:SearchItemStatusNormal timestamp:[now dateByAddingTimeInterval:-2*dayInterval] popularity:75]
    ]];
    
    // 猜你想搜数据
    [self.mutableSuggestions addObjectsFromArray:@[
        [[SearchItem alloc] initWithId:@"suggestion_1" title:@"孕早期肚子隐隐作痛" subtitle:@"" type:SearchItemTypeSuggestion status:SearchItemStatusNormal timestamp:now popularity:90],
        [[SearchItem alloc] initWithId:@"suggestion_2" title:@"宫外孕的早期症状" subtitle:@"" type:SearchItemTypeSuggestion status:SearchItemStatusNormal timestamp:now popularity:88],
        [[SearchItem alloc] initWithId:@"suggestion_3" title:@"安全度过孕早期" subtitle:@"" type:SearchItemTypeSuggestion status:SearchItemStatusNormal timestamp:now popularity:82],
        [[SearchItem alloc] initWithId:@"suggestion_4" title:@"hcg" subtitle:@"孕早期怎么知道宝宝在..." type:SearchItemTypeSuggestion status:SearchItemStatusNormal timestamp:now popularity:80],
        [[SearchItem alloc] initWithId:@"suggestion_5" title:@"孕酮" subtitle:@"" type:SearchItemTypeSuggestion status:SearchItemStatusNormal timestamp:now popularity:78]
    ]];
    
    // 搜索热点数据
    [self.mutableHotTopics addObjectsFromArray:@[
        [[SearchItem alloc] initWithId:@"hot_1" title:@"几周有胎心胎芽是正常的" subtitle:@"" type:SearchItemTypeHotTopic status:SearchItemStatusNormal timestamp:now popularity:95],
        [[SearchItem alloc] initWithId:@"hot_2" title:@"肚子上长毛毛，暗示了什么" subtitle:@"热" type:SearchItemTypeHotTopic status:SearchItemStatusNormal timestamp:now popularity:92],
        [[SearchItem alloc] initWithId:@"hot_3" title:@"孕早期肚子隐隐作痛要警惕" subtitle:@"荐" type:SearchItemTypeHotTopic status:SearchItemStatusNormal timestamp:now popularity:89],
        [[SearchItem alloc] initWithId:@"hot_4" title:@"珍贵守护，挚爱之选" subtitle:@"" type:SearchItemTypeHotTopic status:SearchItemStatusNormal timestamp:now popularity:87],
        [[SearchItem alloc] initWithId:@"hot_5" title:@"怀孕前三个月不能说的禁忌" subtitle:@"" type:SearchItemTypeHotTopic status:SearchItemStatusNormal timestamp:now popularity:85],
        [[SearchItem alloc] initWithId:@"hot_6" title:@"如何安稳度过怀孕头三个月" subtitle:@"" type:SearchItemTypeHotTopic status:SearchItemStatusNormal timestamp:now popularity:83],
        [[SearchItem alloc] initWithId:@"hot_7" title:@"孕早期怎么知道宝宝在肚子里好不好" subtitle:@"" type:SearchItemTypeHotTopic status:SearchItemStatusNormal timestamp:now popularity:81],
        [[SearchItem alloc] initWithId:@"hot_8" title:@"孕早期能同房吗" subtitle:@"新" type:SearchItemTypeHotTopic status:SearchItemStatusNormal timestamp:now popularity:79],
        [[SearchItem alloc] initWithId:@"hot_9" title:@"nt怎么看出男宝女宝" subtitle:@"" type:SearchItemTypeHotTopic status:SearchItemStatusNormal timestamp:now popularity:77],
        [[SearchItem alloc] initWithId:@"hot_10" title:@"nt怎么看出来包女宝" subtitle:@"" type:SearchItemTypeHotTopic status:SearchItemStatusNormal timestamp:now popularity:75]
    ]];
}

@end
