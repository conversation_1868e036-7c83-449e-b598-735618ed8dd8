//
//  SearchDataModel.h
//  Augment
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2025/8/7.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

// 搜索项目模型
@interface SearchItem : NSObject

@property (nonatomic, strong) NSString *title;
@property (nonatomic, strong) NSString *subtitle;
@property (nonatomic, strong) NSString *category;
@property (nonatomic, assign) BOOL isRead;
@property (nonatomic, strong) NSDate *timestamp;

- (instancetype)initWithTitle:(NSString *)title 
                     subtitle:(NSString *)subtitle 
                     category:(NSString *)category 
                       isRead:(BOOL)isRead;

@end

// 热点搜索项目模型
@interface HotSearchItem : NSObject

@property (nonatomic, strong) NSString *title;
@property (nonatomic, strong) NSString *tag;
@property (nonatomic, strong) NSString *tagColor; // 标签颜色：pink, orange, yellow, green, blue, purple
@property (nonatomic, assign) NSInteger rank;

- (instancetype)initWithTitle:(NSString *)title 
                          tag:(NSString *)tag 
                     tagColor:(NSString *)tagColor 
                         rank:(NSInteger)rank;

@end

// 数据管理器
@interface SearchDataManager : NSObject

@property (nonatomic, strong, readonly) NSArray<SearchItem *> *searchHistory;
@property (nonatomic, strong, readonly) NSArray<SearchItem *> *recommendedSearches;
@property (nonatomic, strong, readonly) NSArray<HotSearchItem *> *hotSearches;

+ (instancetype)sharedManager;

// 搜索功能
- (NSArray<SearchItem *> *)searchItemsWithKeyword:(NSString *)keyword;

// 搜索历史管理
- (void)addSearchHistoryItem:(SearchItem *)item;
- (void)removeSearchHistoryItem:(SearchItem *)item;
- (void)clearSearchHistory;

// 数据操作
- (void)markItemAsRead:(SearchItem *)item;
- (void)deleteItem:(SearchItem *)item;

@end

NS_ASSUME_NONNULL_END
