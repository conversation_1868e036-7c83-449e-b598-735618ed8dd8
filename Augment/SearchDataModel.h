//
//  SearchDataModel.h
//  Augment
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2025/8/7.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

// 搜索项目类型枚举
typedef NS_ENUM(NSInteger, SearchItemType) {
    SearchItemTypeHistory,      // 搜索历史
    SearchItemTypeSuggestion,   // 猜你想搜
    SearchItemTypeHotTopic      // 搜索热点
};

// 搜索项目状态枚举
typedef NS_ENUM(NSInteger, SearchItemStatus) {
    SearchItemStatusNormal,     // 正常状态
    SearchItemStatusRead,       // 已读状态
    SearchItemStatusUnread      // 未读状态
};

@interface SearchItem : NSObject

@property (nonatomic, strong) NSString *itemId;
@property (nonatomic, strong) NSString *title;
@property (nonatomic, strong) NSString *subtitle;
@property (nonatomic, assign) SearchItemType type;
@property (nonatomic, assign) SearchItemStatus status;
@property (nonatomic, strong) NSDate *timestamp;
@property (nonatomic, assign) NSInteger popularity; // 热度值，用于排序

- (instancetype)initWithId:(NSString *)itemId
                     title:(NSString *)title
                  subtitle:(NSString *)subtitle
                      type:(SearchItemType)type
                    status:(SearchItemStatus)status
                 timestamp:(NSDate *)timestamp
                popularity:(NSInteger)popularity;

@end

@interface SearchDataManager : NSObject

@property (nonatomic, strong, readonly) NSArray<SearchItem *> *searchHistory;
@property (nonatomic, strong, readonly) NSArray<SearchItem *> *suggestions;
@property (nonatomic, strong, readonly) NSArray<SearchItem *> *hotTopics;

+ (instancetype)sharedManager;

// 搜索相关方法
- (NSArray<SearchItem *> *)searchItemsWithKeyword:(NSString *)keyword;
- (void)addSearchHistoryItem:(SearchItem *)item;
- (void)removeSearchHistoryItem:(SearchItem *)item;
- (void)clearSearchHistory;

// 数据操作方法
- (void)markItemAsRead:(SearchItem *)item;
- (void)markItemAsUnread:(SearchItem *)item;
- (void)deleteItem:(SearchItem *)item;

// 数据加载方法
- (void)loadTestData;

@end

NS_ASSUME_NONNULL_END
