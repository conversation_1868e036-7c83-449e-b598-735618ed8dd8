//
//  ViewController.m
//  Augment
//
//  Created by Mee<PERSON><PERSON> on 2025/8/7.
//

#import "ViewController.h"
#import "SearchViewController_ObjC.h"
#import "SearchDataModel.h"
#import <SwiftUI/SwiftUI.h>

// Swift文件导入（通过模块名）
#import "Augment-Swift.h"

@interface ViewController ()

@property (nonatomic, strong) UIStackView *buttonStackView;

@end

@implementation ViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    [self setupUI];
}

- (void)setupUI {
    self.view.backgroundColor = [UIColor systemBackgroundColor];
    self.title = @"搜索应用演示";

    // 创建标题标签
    UILabel *titleLabel = [[UILabel alloc] init];
    titleLabel.text = @"iOS搜索界面\n三种技术栈实现";
    titleLabel.numberOfLines = 0;
    titleLabel.textAlignment = NSTextAlignmentCenter;
    titleLabel.font = [UIFont boldSystemFontOfSize:24];
    titleLabel.textColor = [UIColor labelColor];

    // 创建描述标签
    UILabel *descriptionLabel = [[UILabel alloc] init];
    descriptionLabel.text = @"选择不同的技术栈查看相同功能的实现效果";
    descriptionLabel.numberOfLines = 0;
    descriptionLabel.textAlignment = NSTextAlignmentCenter;
    descriptionLabel.font = [UIFont systemFontOfSize:16];
    descriptionLabel.textColor = [UIColor secondaryLabelColor];

    // 创建按钮
    UIButton *objcButton = [self createButtonWithTitle:@"Objective-C 版本"
                                           description:@"传统iOS开发，使用UIKit框架"
                                                action:@selector(openObjectiveCVersion)];

    UIButton *swiftButton = [self createButtonWithTitle:@"Swift 版本"
                                            description:@"现代iOS开发语言，使用UIKit框架"
                                                 action:@selector(openSwiftVersion)];

    UIButton *swiftUIButton = [self createButtonWithTitle:@"SwiftUI 版本"
                                              description:@"声明式UI框架，最新iOS开发方式"
                                                   action:@selector(openSwiftUIVersion)];

    // 创建垂直堆栈视图
    self.buttonStackView = [[UIStackView alloc] initWithArrangedSubviews:@[objcButton, swiftButton, swiftUIButton]];
    self.buttonStackView.axis = UILayoutConstraintAxisVertical;
    self.buttonStackView.spacing = 20;
    self.buttonStackView.distribution = UIStackViewDistributionFillEqually;

    // 创建主堆栈视图
    UIStackView *mainStackView = [[UIStackView alloc] initWithArrangedSubviews:@[titleLabel, descriptionLabel, self.buttonStackView]];
    mainStackView.axis = UILayoutConstraintAxisVertical;
    mainStackView.spacing = 30;
    mainStackView.alignment = UIStackViewAlignmentFill;

    [self.view addSubview:mainStackView];

    // 设置约束
    mainStackView.translatesAutoresizingMaskIntoConstraints = NO;
    [NSLayoutConstraint activateConstraints:@[
        [mainStackView.centerXAnchor constraintEqualToAnchor:self.view.centerXAnchor],
        [mainStackView.centerYAnchor constraintEqualToAnchor:self.view.centerYAnchor],
        [mainStackView.leadingAnchor constraintGreaterThanOrEqualToAnchor:self.view.leadingAnchor constant:40],
        [mainStackView.trailingAnchor constraintLessThanOrEqualToAnchor:self.view.trailingAnchor constant:-40],

        [self.buttonStackView.heightAnchor constraintEqualToConstant:240] // 3个按钮 * 80高度
    ]];
}

- (UIButton *)createButtonWithTitle:(NSString *)title description:(NSString *)description action:(SEL)action {
    UIButton *button = [UIButton buttonWithType:UIButtonTypeSystem];

    // 设置按钮样式
    button.backgroundColor = [UIColor systemBlueColor];
    button.layer.cornerRadius = 12;
    button.titleLabel.numberOfLines = 0;
    button.titleLabel.textAlignment = NSTextAlignmentCenter;

    // 确保按钮可以接收用户交互
    button.userInteractionEnabled = YES;

    // 创建属性字符串
    NSMutableAttributedString *attributedTitle = [[NSMutableAttributedString alloc] init];

    // 主标题
    NSAttributedString *mainTitle = [[NSAttributedString alloc] initWithString:title
                                                                    attributes:@{
                                                                        NSFontAttributeName: [UIFont boldSystemFontOfSize:18],
                                                                        NSForegroundColorAttributeName: [UIColor whiteColor]
                                                                    }];
    [attributedTitle appendAttributedString:mainTitle];

    // 换行
    [attributedTitle appendAttributedString:[[NSAttributedString alloc] initWithString:@"\n"]];

    // 描述文字
    NSAttributedString *descriptionText = [[NSAttributedString alloc] initWithString:description
                                                                          attributes:@{
                                                                              NSFontAttributeName: [UIFont systemFontOfSize:14],
                                                                              NSForegroundColorAttributeName: [UIColor colorWithWhite:1.0 alpha:0.8]
                                                                          }];
    [attributedTitle appendAttributedString:descriptionText];

    [button setAttributedTitle:attributedTitle forState:UIControlStateNormal];
    [button addTarget:self action:action forControlEvents:UIControlEventTouchUpInside];

    // 调试信息
    NSLog(@"创建按钮: %@, action: %@", title, NSStringFromSelector(action));

    // 设置按钮高度
    [button.heightAnchor constraintEqualToConstant:80].active = YES;

    return button;
}

#pragma mark - Actions

- (void)openObjectiveCVersion {
    NSLog(@"点击了 Objective-C 版本按钮");

    if (!self.navigationController) {
        NSLog(@"错误：navigationController 为 nil");
        return;
    }

    SearchViewController_ObjC *objcViewController = [[SearchViewController_ObjC alloc] init];
    if (objcViewController) {
        NSLog(@"成功创建 SearchViewController_ObjC 实例");
        [self.navigationController pushViewController:objcViewController animated:YES];
    } else {
        NSLog(@"错误：无法创建 SearchViewController_ObjC 实例");
    }
}

- (void)openSwiftVersion {
    NSLog(@"点击了 Swift 版本按钮");

    if (!self.navigationController) {
        NSLog(@"错误：navigationController 为 nil");
        return;
    }

    SearchViewController_Swift *swiftViewController = [[SearchViewController_Swift alloc] init];
    if (swiftViewController) {
        NSLog(@"成功创建 SearchViewController_Swift 实例");
        [self.navigationController pushViewController:swiftViewController animated:YES];
    } else {
        NSLog(@"错误：无法创建 SearchViewController_Swift 实例");
    }
}

- (void)openSwiftUIVersion {
    NSLog(@"点击了 SwiftUI 版本按钮");

    if (!self.navigationController) {
        NSLog(@"错误：navigationController 为 nil");
        return;
    }

    // 创建SwiftUI视图控制器
    SearchViewController_SwiftUI *swiftUIViewController = [[SearchViewController_SwiftUI alloc] init];
    if (swiftUIViewController) {
        NSLog(@"成功创建 SearchViewController_SwiftUI 实例");
        [self.navigationController pushViewController:swiftUIViewController animated:YES];
    } else {
        NSLog(@"错误：无法创建 SearchViewController_SwiftUI 实例");
    }
}

@end
