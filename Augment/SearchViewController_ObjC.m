//
//  SearchViewController_ObjC.m
//  Augment
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2025/8/7.
//

#import "SearchViewController_ObjC.h"
#import "SearchDataModel.h"

@interface SearchViewController_ObjC () <UITableViewDataSource, UITableViewDelegate, UISearchBarDelegate>

@property (nonatomic, strong) UISearchBar *searchBar;
@property (nonatomic, strong) UITableView *tableView;
@property (nonatomic, strong) SearchDataManager *dataManager;
@property (nonatomic, strong) NSArray<SearchItem *> *currentDisplayData;
@property (nonatomic, assign) BOOL isSearching;

@end

@implementation SearchViewController_ObjC

- (void)viewDidLoad {
    [super viewDidLoad];
    [self setupUI];
    [self setupData];
}

- (void)setupUI {
    self.view.backgroundColor = [UIColor systemBackgroundColor];
    self.title = @"搜索 (Objective-C)";
    
    // 设置导航栏
    UIBarButtonItem *backButton = [[UIBarButtonItem alloc] initWithTitle:@"返回" 
                                                                   style:UIBarButtonItemStylePlain 
                                                                  target:self 
                                                                  action:@selector(backButtonTapped)];
    self.navigationItem.leftBarButtonItem = backButton;
    
    // 创建搜索栏
    self.searchBar = [[UISearchBar alloc] init];
    self.searchBar.placeholder = @"搜索";
    self.searchBar.delegate = self;
    self.searchBar.showsCancelButton = YES;
    [self.view addSubview:self.searchBar];
    
    // 创建表格视图
    self.tableView = [[UITableView alloc] init];
    self.tableView.dataSource = self;
    self.tableView.delegate = self;
    self.tableView.backgroundColor = [UIColor systemBackgroundColor];
    [self.tableView registerClass:[UITableViewCell class] forCellReuseIdentifier:@"SearchCell"];
    [self.view addSubview:self.tableView];
    
    // 设置约束
    [self setupConstraints];
}

- (void)setupConstraints {
    self.searchBar.translatesAutoresizingMaskIntoConstraints = NO;
    self.tableView.translatesAutoresizingMaskIntoConstraints = NO;
    
    [NSLayoutConstraint activateConstraints:@[
        [self.searchBar.topAnchor constraintEqualToAnchor:self.view.safeAreaLayoutGuide.topAnchor],
        [self.searchBar.leadingAnchor constraintEqualToAnchor:self.view.leadingAnchor],
        [self.searchBar.trailingAnchor constraintEqualToAnchor:self.view.trailingAnchor],
        
        [self.tableView.topAnchor constraintEqualToAnchor:self.searchBar.bottomAnchor],
        [self.tableView.leadingAnchor constraintEqualToAnchor:self.view.leadingAnchor],
        [self.tableView.trailingAnchor constraintEqualToAnchor:self.view.trailingAnchor],
        [self.tableView.bottomAnchor constraintEqualToAnchor:self.view.bottomAnchor]
    ]];
}

- (void)setupData {
    self.dataManager = [SearchDataManager sharedManager];
    [self loadInitialData];
}

- (void)loadInitialData {
    self.isSearching = NO;
    NSMutableArray *allData = [[NSMutableArray alloc] init];
    
    // 添加搜索历史
    if (self.dataManager.searchHistory.count > 0) {
        [allData addObjectsFromArray:self.dataManager.searchHistory];
    }
    
    // 添加猜你想搜
    if (self.dataManager.suggestions.count > 0) {
        [allData addObjectsFromArray:self.dataManager.suggestions];
    }
    
    // 添加搜索热点
    if (self.dataManager.hotTopics.count > 0) {
        [allData addObjectsFromArray:self.dataManager.hotTopics];
    }
    
    self.currentDisplayData = [allData copy];
    [self.tableView reloadData];
}

#pragma mark - Actions

- (void)backButtonTapped {
    [self.navigationController popViewControllerAnimated:YES];
}

#pragma mark - UISearchBarDelegate

- (void)searchBar:(UISearchBar *)searchBar textDidChange:(NSString *)searchText {
    if (searchText.length == 0) {
        [self loadInitialData];
    } else {
        self.isSearching = YES;
        self.currentDisplayData = [self.dataManager searchItemsWithKeyword:searchText];
        [self.tableView reloadData];
    }
}

- (void)searchBarCancelButtonClicked:(UISearchBar *)searchBar {
    searchBar.text = @"";
    [searchBar resignFirstResponder];
    [self loadInitialData];
}

- (void)searchBarSearchButtonClicked:(UISearchBar *)searchBar {
    [searchBar resignFirstResponder];
    
    if (searchBar.text.length > 0) {
        // 添加到搜索历史
        SearchItem *historyItem = [[SearchItem alloc] initWithId:[NSString stringWithFormat:@"search_%@", @([[NSDate date] timeIntervalSince1970])]
                                                           title:searchBar.text
                                                        subtitle:@""
                                                            type:SearchItemTypeHistory
                                                          status:SearchItemStatusNormal
                                                       timestamp:[NSDate date]
                                                      popularity:50];
        [self.dataManager addSearchHistoryItem:historyItem];
    }
}

#pragma mark - UITableViewDataSource

- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView {
    if (self.isSearching) {
        return 1;
    }
    
    NSInteger sections = 0;
    if (self.dataManager.searchHistory.count > 0) sections++;
    if (self.dataManager.suggestions.count > 0) sections++;
    if (self.dataManager.hotTopics.count > 0) sections++;
    return sections;
}

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    if (self.isSearching) {
        return self.currentDisplayData.count;
    }
    
    NSInteger currentSection = 0;
    
    if (self.dataManager.searchHistory.count > 0) {
        if (section == currentSection) {
            return self.dataManager.searchHistory.count;
        }
        currentSection++;
    }
    
    if (self.dataManager.suggestions.count > 0) {
        if (section == currentSection) {
            return self.dataManager.suggestions.count;
        }
        currentSection++;
    }
    
    if (self.dataManager.hotTopics.count > 0) {
        if (section == currentSection) {
            return self.dataManager.hotTopics.count;
        }
    }
    
    return 0;
}

- (NSString *)tableView:(UITableView *)tableView titleForHeaderInSection:(NSInteger)section {
    if (self.isSearching) {
        return @"搜索结果";
    }
    
    NSInteger currentSection = 0;
    
    if (self.dataManager.searchHistory.count > 0) {
        if (section == currentSection) {
            return @"搜索历史";
        }
        currentSection++;
    }
    
    if (self.dataManager.suggestions.count > 0) {
        if (section == currentSection) {
            return @"猜你想搜";
        }
        currentSection++;
    }
    
    if (self.dataManager.hotTopics.count > 0) {
        if (section == currentSection) {
            return @"搜索热点";
        }
    }
    
    return nil;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    UITableViewCell *cell = [tableView dequeueReusableCellWithIdentifier:@"SearchCell" forIndexPath:indexPath];
    
    SearchItem *item;
    
    if (self.isSearching) {
        item = self.currentDisplayData[indexPath.row];
    } else {
        NSInteger currentSection = 0;
        
        if (self.dataManager.searchHistory.count > 0) {
            if (indexPath.section == currentSection) {
                item = self.dataManager.searchHistory[indexPath.row];
            }
            currentSection++;
        }
        
        if (!item && self.dataManager.suggestions.count > 0) {
            if (indexPath.section == currentSection) {
                item = self.dataManager.suggestions[indexPath.row];
            }
            currentSection++;
        }
        
        if (!item && self.dataManager.hotTopics.count > 0) {
            if (indexPath.section == currentSection) {
                item = self.dataManager.hotTopics[indexPath.row];
            }
        }
    }
    
    if (item) {
        cell.textLabel.text = item.title;
        cell.detailTextLabel.text = item.subtitle.length > 0 ? item.subtitle : nil;
        
        // 根据类型设置不同的样式
        switch (item.type) {
            case SearchItemTypeHistory:
                cell.imageView.image = [UIImage systemImageNamed:@"clock"];
                break;
            case SearchItemTypeSuggestion:
                cell.imageView.image = [UIImage systemImageNamed:@"magnifyingglass"];
                break;
            case SearchItemTypeHotTopic:
                cell.imageView.image = [UIImage systemImageNamed:@"flame"];
                break;
        }
        
        // 设置副标题颜色
        if (item.subtitle.length > 0) {
            if ([item.subtitle isEqualToString:@"热"]) {
                cell.detailTextLabel.textColor = [UIColor systemRedColor];
            } else if ([item.subtitle isEqualToString:@"荐"]) {
                cell.detailTextLabel.textColor = [UIColor systemOrangeColor];
            } else if ([item.subtitle isEqualToString:@"新"]) {
                cell.detailTextLabel.textColor = [UIColor systemBlueColor];
            } else {
                cell.detailTextLabel.textColor = [UIColor systemGrayColor];
            }
        }
    }
    
    return cell;
}

#pragma mark - UITableViewDelegate

- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath {
    [tableView deselectRowAtIndexPath:indexPath animated:YES];
    
    SearchItem *item;
    if (self.isSearching) {
        item = self.currentDisplayData[indexPath.row];
    } else {
        // 获取对应的item（简化处理）
        NSArray *allItems = [self getAllItems];
        if (indexPath.section < allItems.count) {
            NSArray *sectionItems = allItems[indexPath.section];
            if (indexPath.row < sectionItems.count) {
                item = sectionItems[indexPath.row];
            }
        }
    }
    
    if (item) {
        // 显示详情或执行搜索
        UIAlertController *alert = [UIAlertController alertControllerWithTitle:@"选中项目" 
                                                                       message:[NSString stringWithFormat:@"您选择了：%@", item.title] 
                                                                preferredStyle:UIAlertControllerStyleAlert];
        [alert addAction:[UIAlertAction actionWithTitle:@"确定" style:UIAlertActionStyleDefault handler:nil]];
        [self presentViewController:alert animated:YES completion:nil];
    }
}

- (NSArray *)getAllItems {
    NSMutableArray *allSections = [[NSMutableArray alloc] init];
    
    if (self.dataManager.searchHistory.count > 0) {
        [allSections addObject:self.dataManager.searchHistory];
    }
    
    if (self.dataManager.suggestions.count > 0) {
        [allSections addObject:self.dataManager.suggestions];
    }
    
    if (self.dataManager.hotTopics.count > 0) {
        [allSections addObject:self.dataManager.hotTopics];
    }
    
    return [allSections copy];
}

@end
